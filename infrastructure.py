import os
import consul  # 引入consul库
import requests
import json
from autogen_agentchat.messages import  ModelClientStreamingChunkEvent, ToolCallExecutionEvent, ToolCallRequestEvent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, ToolCallExecutionEvent, ToolCallSummaryMessage
from autogen_ext.tools.mcp import   McpServerParams, McpWorkbench
from autogen_agentchat.base._chat_agent import Response
from autogen_core.tools import ToolResult
from autogen_core import CancellationToken
from loguru import logger
import time
from typing import Dict,Any,Callable,Awaitable,Generator,AsyncGenerator
from enum import Enum
from fastapi.responses import StreamingResponse
from fastapi import FastAPI,Request
class Application:
    methodPtr:Callable[[requests.Request],Generator[dict[str,any]]|AsyncGenerator[dict[str,any]]]=None
    def __init__(self, methodPtr:Callable[[requests.Request],Generator[dict[str,any]]|AsyncGenerator[dict[str,any]]]=None ):
        Application.methodPtr=methodPtr
   
    apiApp=FastAPI()
    @apiApp.get("/healthz")
    def healthz():
        return {"status":"ok"}
    @apiApp.get("/sse")
    async def sse_event_stream(request: Request):
        return StreamingResponse(Application.sse_event_streamImpl(request), media_type="text/event-stream") 
    @staticmethod
    async def sse_event_streamImpl( request: Request):
        responseItems=Application.methodPtr(request)
        try:
            if isinstance (responseItems,AsyncGenerator):
                async for d in responseItems:
                    yield f"data:  {d}\n\n"
            else:
                for d in responseItems:
                    yield f"data:  {d}\n\n"
        except Exception as e:
            yield f"event: error\ndata: {str(e)}\n\n"   
         
    #注册服务到consul中，通常由框架调用
    def  run(self,serviceName:str):
        # 从环境变量中获取 Consul 地址、宿主机 IP 和端口
        consul_address = os.getenv('ConsulConfigAddress', 'http://*************:8500')
        host_ip = os.getenv('DOTNET_HOST_IP', '127.0.0.1')
        port = os.getenv('DOTNET_HTTP_PORT', 8000)
    
        # 构造 JSON 数据
        json_payload = {
            "ID": "xazf",
            "Name": serviceName,
            "Address": host_ip,
            "Port": int(port),
            "Check": {
                "DeregisterCriticalServiceAfter": "1m",
                "HTTP": f"http://{host_ip}:{port}/healthz",
                "Interval": "10s"
            }
        }

        # 发送 POST 请求到 Consul 注册服务
        url = f"{consul_address}/v1/agent/service/register"
        headers = {'Content-Type': 'application/json'}

        try:
            response = requests.put(url, data=json.dumps(json_payload), headers=headers)
            response.raise_for_status()  # 如果响应状态码不是 200，将引发 HTTPError 异常
            print(f"服务已注册到 Consul: {consul_address}")
        except requests.exceptions.RequestException as e:
            print(f"注册服务到 Consul 时发生错误: {e}")
        
       
        import uvicorn
        uvicorn.run(app=self.apiApp, host="0.0.0.0", port=int(port))


#对应工具调用的结果上下文
class WorkbenchCallToolResultContext:
     def __init__(self,name: str, arguments: Dict[str, Any] | None = None,result:ToolResult|None=None):
         self.name=name
         self.arguments=arguments
         self.result=result
         
class LogLevel(Enum):
    Trace = 0
    Debug = 1
    Information = 2
    Warning = 3
    Error = 4
    Critical = 5
class WriteMsg:
    question = ""
    data_tka = list()
    data_keyword = list()
    data_author = list()
    tool_execution_data = []  # 新增工具执行数据存储
    
    def writeSSEMessage(self, message):
        body=None
        if isinstance(message,ModelClientStreamingChunkEvent ):
            body={'type':'message','text':message.to_text(),'source':message.source}
        elif isinstance(message,ToolCallSummaryMessage):
           
            body={'type':'tool_call','text':"开始总结",'source':message.source}
        elif isinstance(message,ToolCallExecutionEvent):
           
            data = []
            if self.data_tka:
                data = self.data_tka
            elif self.data_keyword:
                data = self.data_keyword
            elif self.data_author:
                data = self.data_author
            body={'type':'tool_call','text':"搜索中", "data":data,'source':message.source}
            
        elif isinstance(message,ToolCallRequestEvent):
            body={'type':'tool_req','text':"开始查找",'source':message.source}
        elif isinstance(message,Response):
            body={'type':'response','text':message.chat_message.to_text(),'source':"final"}

        return body
    
    def get_all_tool_executions(self):
        """获取所有工具执行数据"""
        return self.tool_execution_data
    
    def get_latest_tool_execution(self):
        """获取最新的工具执行数据"""
        return self.tool_execution_data[-1] if self.tool_execution_data else None
    
    def clear_tool_executions(self):
        """清空工具执行数据"""
        self.tool_execution_data.clear()
# 增强的消息处理类，支持工具执行数据收集
class EnhancedWriteMsg(WriteMsg):
    def __init__(self):
        super().__init__()
        self.tool_execution_data = []
        self.workbench_sessions = {}
    
    def add_tool_execution_data(self, tool_name: str, arguments: Dict[str, Any], result: ToolResult):
        """添加工具执行数据"""
        execution_data = {
            'tool_name': tool_name,
            'arguments': arguments,
            'result': result.to_text() if hasattr(result, 'to_text') else str(result),
            'timestamp': time.time(),
            'is_error': result.is_error if hasattr(result, 'is_error') else False
        }
        self.tool_execution_data.append(execution_data)
        
    def get_tool_execution_summary(self):
        """获取工具执行摘要"""
        return {
            'total_executions': len(self.tool_execution_data),
            'executions': self.tool_execution_data[-10:],  # 返回最后10次执行
            'latest_execution': self.tool_execution_data[-1] if self.tool_execution_data else None
        }
# 安全的 McpWorkbench 包装类，避免析构时的异步操作
class SafeMcpWorkbench(McpWorkbench):
    def __init__(self, server_params):
        super().__init__(server_params)
        self._is_safely_closed = False
    
    def __del__(self):
        """重写析构函数，避免在事件循环关闭时执行异步操作"""
        try:
            # 标记为已安全关闭，不执行任何异步操作
            self._is_safely_closed = True
            # 清理可能的引用但不调用异步方法
            if hasattr(self, '_session'):
                self._session = None
        except Exception:
            # 忽略所有析构错误
            pass
# 自定义工作台装饰器，用于监控工具执行
class MonitoredMcpWorkbench(McpWorkbench):
    def __init__(self, server_params, callToolCallback:Callable[[WorkbenchCallToolResultContext],Awaitable[ToolResult]]):
        super().__init__(server_params[0])
        self.callToolCallback = callToolCallback
        # 将类变量改为实例变量
        self.workbenchList = []
        self.toolNameMap = {}
       
        for item in server_params:
            self.workbenchList.append(SafeMcpWorkbench(item))
            
    async def list_tools(self):
        all_tools=[]
        for item in self.workbenchList:
           tools=await item.list_tools()
           for tool in tools:
               #print(f'the tool is {tool}')
               self.toolNameMap[tool['name']]=item
           all_tools.extend(tools)
        return all_tools
    
    async def call_tool(self, name: str, arguments: Dict[str, Any] | None = None, cancellation_token: CancellationToken | None = None) -> ToolResult:
        """重写call_tool方法以监控工具执行"""
        # 调用原始工具
        work:McpWorkbench = self.toolNameMap[name]
        tool_result =  await work.call_tool(name, arguments, cancellation_token)
        toolContext=WorkbenchCallToolResultContext(name=name,arguments=arguments,result=tool_result)
        return  await  self.callToolCallback(toolContext)   
            

    def __del__(self):
        """重写析构函数，避免在事件循环关闭时执行异步操作导致RuntimeWarning"""
        try:
            # 清理工作台列表中的引用，但不调用异步方法
            if hasattr(self, 'workbenchList'):
                for workbench in self.workbenchList:
                    # 标记 SafeMcpWorkbench 为已安全关闭状态
                    if hasattr(workbench, '_is_safely_closed'):
                        workbench._is_safely_closed = True
                    # 清理可能的会话引用
                    if hasattr(workbench, '_session'):
                        workbench._session = None
                self.workbenchList.clear()
                
            # 清理工具名称映射
            if hasattr(self, 'toolNameMap'):
                self.toolNameMap.clear()
                
            # 清理自身的引用
            if hasattr(self, '_session'):
                self._session = None
                
            # 标记自身为已关闭
            self._is_safely_closed = True
            
        except Exception:
            # 忽略析构过程中的所有错误，避免进一步的问题
            pass
#返回一个供Agent调用MCP工具的McpWorkbench
def ensureWorkbench(serviceParameters:list[McpServerParams],callbakMessage:Callable[[WorkbenchCallToolResultContext],Awaitable[ToolResult]])->McpWorkbench:
    """
    确保工作台的创建。
    Args:
        serviceParameters (list[McpServerParams]): 服务参数列表。
        callbakMessage (Any): 回调消息，用以对mcp工具返回的信息进行预处理，参数类型为toolresult，见https://microsoft.github.io/autogen/stable/reference/python/autogen_core.tools.html#autogen_core.tools.ToolResult
    Returns:
        McpWorkbench: 返回工作台实例。
    """
    return MonitoredMcpWorkbench(serviceParameters,callbakMessage)

#此函数用以记录日志，请勿直接使用print输出日志
def writeLog(logStr:str,logLevel:LogLevel = LogLevel.Information):
    if logLevel == LogLevel.Information:
        logger.info(logStr)
    elif logLevel == LogLevel.Warning:
        logger.warning(logStr)
    elif logLevel == LogLevel.Error:
        logger.error(logStr)
    elif logLevel == LogLevel.Debug:
        logger.debug(logStr)
    elif logLevel == LogLevel.Critical:
        logger.critical(logStr)
    else :
        logger.error(logStr)
    
    

#获取指定mcp服务的地址
def ensureMcpServerUrl(mcpServerName: str) -> str:
    """
    返回指定mcp服务的地址，从consul查询。

    Args:
        mcpServerName (str): MCP 服务器的名称。

    Returns:
        str: 处理后的 URL 字符串。
    """
    consul_address = os.getenv('ConsulConfigAddress', 'http://**************:8500')
    consul_addressArray =consul_address.split(':')
    host = consul_addressArray[1].replace('//','') if len(consul_addressArray) > 1 else 'localhost'
    port = int(consul_addressArray[2]) if len(consul_addressArray) > 2 else 8500

    # 创建Consul客户端
    consul_client = consul.Consul(host=host, port=port)
    try:
        # 查询服务地址
        services = consul_client.health.service(mcpServerName)
         
        if services[1]:  # 检查服务列表是否存在
            for service in services[1]:
                instance_info = {
                    'service_id': service['Service']['ID'],
                    'service_name': service['Service']['Service'],
                    'address': service['Service']['Address'],
                    'port': service['Service']['Port']
                }
                return f'http://{instance_info["address"]}:{instance_info["port"]}/sse'
        else:
            raise ValueError(f"Service '{mcpServerName}' not found in Consul")
    except Exception as e:
        raise ValueError(f"Failed to retrieve service '{mcpServerName}' from Consul: {str(e)}")
    
def ensureMcpServerParameter(mcpServerName: str)->McpServerParams:
    """
    确保MCP服务器的参数。

    Args:
        mcpServerName (str): MCP服务器的名称。

    Returns:
        McpServerParams: MCP服务器的参数。

    """
    serverUrl=ensureMcpServerUrl(mcpServerName)
    from autogen_ext.tools.mcp import StreamableHttpServerParams
    return StreamableHttpServerParams(
        url=serverUrl,
        headers={'user-agent': '234'}
    )
