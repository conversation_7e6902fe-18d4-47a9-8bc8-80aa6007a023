[project]
name = "krs_agent"
version = "0.1.0"
description = ""
authors = [
    
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "autogen-agentchat>=0.6.1",
    "autogen-core>=0.6.1",
    "autogen-ext[mcp,openai]>=0.6.1",
    
    "python-consul>=1.1.0",
    
    "fastapi (>=0.116.0,<0.117.0)",
    
    "uvicorn (>=0.35.0,<0.36.0)",
    
    "loguru (>=0.7.3,<0.8.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
