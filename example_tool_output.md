# 工具检索结果输出示例

## 概述
修改后的app1.py会在工具执行时打印详细的检索结果，以下是各种工具的输出示例。

## TKA搜索工具输出示例

### 1. 工具执行开始
```
=== 工具执行结果 ===
工具名称: tka_search
原始结果长度: 2847 字符
原始结果前500字符: [{"title": "深度学习在医学影像诊断中的应用研究", "author": "张三;李四", "pub_year": "2023", "abstract": "本文综述了深度学习技术在医学影像诊断领域的最新进展..."}]...
```

### 2. 数据解析结果
```
解析结果: 列表类型，包含 25 个项目
第一个项目示例: {
  "title": "深度学习在医学影像诊断中的应用研究",
  "author": "张三;李四",
  "pub_year": "2023",
  "abstract": "本文综述了深度学习技术在医学影像诊断领域的最新进展..."
}
```

### 3. 详细文献信息
```
处理TKA/标题搜索结果...
TKA搜索原始结果: 25 篇文献
  文献 1:
    标题: 深度学习在医学影像诊断中的应用研究
    作者: 张三;李四
    年份: 2023
    摘要: 本文综述了深度学习技术在医学影像诊断领域的最新进展，包括卷积神经网络、循环神经网络等...
  文献 2:
    标题: 基于深度学习的医学图像分割算法研究
    作者: 王五;赵六
    年份: 2022
    摘要: 医学图像分割是医学影像分析的重要环节，本文提出了一种基于深度学习的自动分割方法...
```

### 4. 文献筛选过程
```
准备进行文献筛选，候选文献: 25 篇
TKA搜索筛选完成，最终选择: 8 篇文献
  筛选文献 1:
    标题: 深度学习在医学影像诊断中的应用研究
    作者: 张三;李四
  筛选文献 2:
    标题: 基于深度学习的医学图像分割算法研究
    作者: 王五;赵六
TKA搜索处理完成，数据已保存到 msg_obj.data_tka
```

## 主题词搜索工具输出示例

### 1. 搜索结果概览
```
处理主题词搜索结果...
主题词搜索原始结果: 42 篇文献
  文献 1: 深度学习技术在智能制造中的应用
  文献 2: 机器学习算法优化研究进展
  文献 3: 人工智能在医疗诊断中的创新应用
  文献 4: 神经网络模型的改进与优化
  文献 5: 计算机视觉技术发展趋势分析
```

### 2. 筛选过程
```
准备进行文献筛选，候选文献: 42 篇
主题词搜索筛选完成，最终选择: 12 篇文献
  筛选文献 1: 深度学习技术在智能制造中的应用
  筛选文献 2: 机器学习算法优化研究进展
  筛选文献 3: 人工智能在医疗诊断中的创新应用
主题词搜索处理完成，数据已保存到 msg_obj.data_keyword
```

## 作者搜索工具输出示例

### 1. 作者信息详情
```
处理作者搜索结果...
作者搜索成功，找到 3 位作者:
  作者 1:
    姓名: 张三
    信息: 清华大学计算机科学与技术系教授，主要研究方向为深度学习、计算机视觉
    文章数: 15
    文章示例: 深度学习在医学影像诊断中的应用研究
  作者 2:
    姓名: 李四
    信息: 北京大学信息科学技术学院副教授，专注于机器学习算法研究
    文章数: 12
    文章示例: 基于强化学习的智能决策系统设计
  作者 3:
    姓名: 王五
    信息: 中科院自动化研究所研究员，人工智能领域专家
    文章数: 18
    文章示例: 多模态深度学习模型的构建与应用
```

### 2. 处理完成
```
作者搜索处理完成，数据已保存到 msg_obj.data_author
```

## 错误处理示例

### 1. JSON解析错误
```
=== 工具执行结果 ===
工具名称: subject_search
原始结果长度: 45 字符
原始结果前500字符: {"error": "搜索服务暂时不可用"}...
解析结果: 字典类型，包含键: ['error']
错误信息: 搜索服务暂时不可用
主题词搜索无结果或格式错误: {'error': '搜索服务暂时不可用'}
```

### 2. 网络连接错误
```
处理工具结果时出错: HTTPConnectionPool(host='api.example.com', port=443): Max retries exceeded
错误堆栈: Traceback (most recent call last):
  File "app1.py", line 215, in process_tool_result
    data_tool = json.loads(result)
  ...
错误处理完成，返回原始结果
```

## 查看日志的方法

### 1. 控制台输出
直接运行应用时，所有日志会输出到控制台：
```bash
python app1.py
```

### 2. 日志文件
如果配置了日志文件，可以查看详细的执行记录：
```bash
tail -f logs/app.log
```

### 3. 测试脚本
使用提供的测试脚本查看工具执行过程：
```bash
python test_tool_results.py
```

## 日志级别说明

- **信息级别**: 正常的工具执行过程
- **警告级别**: 数据格式异常但可以继续处理
- **错误级别**: 工具执行失败或数据解析错误

## 性能监控

通过日志可以监控：
- 工具执行时间
- 数据处理效率
- 筛选算法效果
- 错误发生频率

这些信息有助于优化系统性能和改进搜索算法。
