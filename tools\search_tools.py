"""
搜索工具定义模块
提供各种搜索功能的工具定义
"""
import json
import requests
from typing import Dict, Any, List
from autogen_core.tools import Tool, FunctionTool

# 搜索API配置
SEARCH_CONFIG = {
    "appid": "aisearch",
    "type": "2",
    "SiteId": "83",
    "SiteUserName": "ztdata",
    "SitePassword": "qwl!3XH4qB6",
    "base_url": "https://api.vipslib.com/testesapi/api/v4/elasticsearch/SearchInTable",
    "multi_search_url": "https://api.vipslib.com/testesapi/api/v4/elasticsearch/MultiSearchInTable"
}


def get_search_headers() -> Dict[str, str]:
    """
    获取搜索API的通用请求头
    
    Returns:
        Dict[str, str]: 包含认证信息的请求头字典
    """
    return {
        "appid": SEARCH_CONFIG["appid"],
        "type": SEARCH_CONFIG["type"],
        'SiteId': SEARCH_CONFIG["SiteId"],
        'SiteUserName': SEARCH_CONFIG["SiteUserName"],
        'SitePassword': SEARCH_CONFIG["SitePassword"],
        'Content-Type': 'application/json'
    }


async def tka_search(tka_all: str, author_all: str = "", organ: str = "", pub_year: str = "") -> str:
    """
    篇关摘搜索工具 - 根据篇名、摘要、关键词（篇关摘）同时进行检索，搜索相关文章
    
    Args:
        tka_all: 篇关摘搜索词，多个搜索词用分号分隔，如："深度学习;机器学习"
        author_all: 作者姓名，可选参数
        organ: 机构名称，可选参数
        pub_year: 文章发表年份，可选参数
        
    Returns:
        str: JSON格式的搜索结果
    """
    try:
        if not tka_all:
            return json.dumps({"error": "未提取到篇关摘tka"}, ensure_ascii=False)
        
        keyword = {
            "tka_all": tka_all,
            "author_all": author_all,
            "organ": organ,
            "pub_year": pub_year
        }
        
        rule = ""
        ruleList = []
        ruleParam = []
        filed_type = {
            "tka_all": "phrase",
            "author_all": "phrase",
            "organ": "phrase",
            "pub_year": "x"
        }
        
        tka_all_list = tka_all.split(";")
        if len(tka_all_list) == 1:
            tka_all_list = tka_all.split(" ")
        
        try:
            i = 0
            key_json = keyword
            for k, v in key_json.items():
                if not v.strip():
                    continue
                if k == "tka_all":
                    tka_rule_list = []
                    for v in tka_all_list:
                        ruleParam.append({"k": str(i), "v": v})
                        tka_rule_list.append(f"(tka_all={filed_type[k]}_{i})")
                        i += 1
                    # Tka之间用OR连接
                    tka_or_rule = " OR ".join(tka_rule_list)
                    ruleList.append(tka_or_rule)
                else:
                    ruleParam.append({"k": str(i), "v": v})
                    ruleList.append(f"({k}={filed_type[k]}_{i})")
                i += 1
            rule = " AND ".join(ruleList)
        except Exception as e:
            rule = "(tka_all=phrase_p1t2eygh)"
            ruleParam = [{"k": "p1t2eygh", "v": tka_all}]

        url = SEARCH_CONFIG["base_url"]

        payload = json.dumps({
            "tableName": "zt_product_doc",
            "searchMode": 1,
            "owner": "krs",
            "userWord": tka_all,
            "isHighlight": 1,
            "rule": rule,
            "ruleParam": ruleParam,
            "isSynonym": 0,
            "filterRule": "collection=x_1",
            "filterParam": [
                {
                    "k": "1",
                    "v": "krs"
                }
            ],
            "resultField": "title;author;pub_year;abstract",
            "pageNow": 1,
            "pageSize": 50,
            "sort": "",
            "aggsType": "",
            "aggsField": "",
            "filter4count": "",
            "noTruncateResultField": "",
            "fieldTruncateLength": 1000,
            "prioField": "",
            "minShouldMatch": "",
            "refreshCache": False
        }, ensure_ascii=False)
        
        headers = get_search_headers()

        response = requests.post(url, headers=headers, data=payload, timeout=10)
        
        if response.status_code != 200:
            return json.dumps({"error": f"请求失败，状态码: {response.status_code}"}, ensure_ascii=False)
        
        response_data = response.json()
        data = response_data.get('hits')
        
        if not data:
            return json.dumps({"error": f"返回错误内容: {response.text}"}, ensure_ascii=False)
        
        hists = data
        if not hists:
            return json.dumps({"message": "没有搜索到相关内容"}, ensure_ascii=False)
        
        return json.dumps(hists.get('source'), ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({"error": f"篇关摘搜索失败: {str(e)}"}, ensure_ascii=False)


async def title_search(title: str) -> str:
    """
    标题搜索工具 - 根据标题关键词搜索学术文献
    
    Args:
        title: 标题搜索，支持多个标题用分号(;)分隔，如："深度学习;机器学习;人工智能"
        
    Returns:
        str: JSON格式的搜索结果
    """
    try:
        # 验证参数
        if not title:
            return json.dumps({"error": "title必须为非空"}, ensure_ascii=False)
        
        # 构建查询规则
        rule_parts = []
        ruleParam = []

        # 处理标题参数
        if title:
            title_list = title.split(";")
            title_rules = []
            for i, sub in enumerate(title_list):
                title_rules.append(f"(title_all=phrase_{i})")
                ruleParam.append({"k": str(i), "v": sub})
            
            if title_rules:
                rule_parts.append("(" + " OR ".join(title_rules) + ")")
        
        # 组合查询规则
        if len(rule_parts) > 1:
            rule = " AND ".join(rule_parts)
        else:
            rule = rule_parts[0] if rule_parts else ""
         
        url = SEARCH_CONFIG["base_url"]

        payload = json.dumps({
            "tableName": "zt_product_doc",
            "searchMode": 1,
            "owner": "krs",
            "userWord": title,
            "rule": rule,
            "ruleParam": ruleParam,
            "isSynonym": 0,
            "filterRule": "owner=x_1",
            "filterParam": [
                {
                    "k": "1",
                    "v": "krs"
                }
            ],
            "resultField": "title;author;pub_year;abstract",
            "pageNow": 1,
            "pageSize": 50,
            "sort": "",
            "aggsType": "",
            "aggsField": "",
            "filter4count": "",
            "noTruncateResultField": "",
            "fieldTruncateLength": 1000,
            "prioField": "",
            "minShouldMatch": "",
            "refreshCache": False
        }, ensure_ascii=False)
        
        headers = get_search_headers()

        response = requests.post(url, headers=headers, data=payload, timeout=10)
        
        if response.status_code != 200:
            return json.dumps({"error": f"请求失败，状态码: {response.status_code}"}, ensure_ascii=False)
        
        response_data = response.json()
        data = response_data.get('hits')
        
        if not data:
            return json.dumps({"error": f"返回错误内容: {response.text}"}, ensure_ascii=False)
        
        hists = data
        if not hists:
            return json.dumps({"message": "没有搜索到相关内容"}, ensure_ascii=False)
        
        return json.dumps(hists.get('source'), ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({"error": f"标题搜索失败: {str(e)}"}, ensure_ascii=False)


async def author_search(search_params: str) -> str:
    """
    作者搜索工具 - 根据作者、主题和机构搜索学术文献
    
    Args:
        search_params: JSON格式的搜索参数，包含author, subject, organ字段
        
    Returns:
        str: JSON格式的搜索结果
    """
    try:
        params = json.loads(search_params) if isinstance(search_params, str) else search_params
        author = params.get('author', '')
        subject = params.get('subject', '')
        organ = params.get('organ', '')
        
        # 验证参数
        if not author and not subject and not organ:
            return json.dumps({"error": "至少需要提供author、subject或organ中的一个参数"}, ensure_ascii=False)
        
        # Helper function to create filter rules and parameters
        def create_filter_rule_and_param(key, value, prefix=""):
            if value:
                return f"({key}={prefix}{key})", [{"k": key, "v": value}]
            return "", []

        filterParam = []
        # Create filter rules and parameters
        organ_filterRule, organ_filterParam = create_filter_rule_and_param("organ_all", organ, "phrase_")
        author_filterRule, author_filterParam = create_filter_rule_and_param("author", author, "x_")

        subject_filterRule_list = []
        if subject:
            subject_list = subject.split(";")
            for i, sub in enumerate(subject_list):
                subject_filterRule_list.append(f"(subject=x_{i})")
                filterParam.append({"k": str(i), "v": sub})

        # Combine all filter parameters
        filterParam += author_filterParam + organ_filterParam

        # Construct the filter rule
        filterRule_parts = [author_filterRule, organ_filterRule]
        if subject_filterRule_list:
            filterRule_parts.append("(" + " OR ".join(subject_filterRule_list) + ")")
        filterRule = " AND ".join(filter for filter in filterRule_parts if filter)
            
        url = SEARCH_CONFIG["base_url"]

        payload = json.dumps({
            "pageSize": 5,
            "resultField": "author_id;subject;author;author_info",
            "filterRule": filterRule,
            "filterParam": filterParam,
            "pageNow": 1,
            "tableName": "zt_ckb_author"
        }, ensure_ascii=False)
        
        headers = get_search_headers()

        response = requests.post(url, headers=headers, data=payload, timeout=10)
        
        if response.status_code != 200:
            return json.dumps({"error": f"请求失败，状态码: {response.status_code}"}, ensure_ascii=False)
        
        response_data = response.json()
        data = response_data.get('hits')
         
        if not data:
            return json.dumps({"error": f"返回错误内容: {response.text}"}, ensure_ascii=False)
        
        hists = data
        if not hists:
            return json.dumps({"message": "没有搜索到相关内容"}, ensure_ascii=False)
        
        # 根据作者id获取对应的发文情况
        author_source = hists.get('source')
        author_ids = [author.get('author_id') for author in author_source]
        body_list = []
        for author_id in author_ids:
            body = {
                "tableName": "zt_product_doc",
                "searchMode": 2,
                "docType": "*",
                "owner": "krs",
                "userWord": "",
                "isHighlight": 0,
                "filterRule": "(owner=x_r1) AND (author_id=x_aid)",
                "filterParam": [
                    {
                        "k": "r1",
                        "v": "krs"
                    },
                    {
                        "k": "aid",
                        "v": author_id
                    }
                ],
                "isSynonym": 0,
                "resultField": "title;abstract;pub_year",
                "pageNow": 1,
                "pageSize": 5,
                "noTruncateResultField": "",
                "fieldTruncateLength": 1000
            }
            body_list.append(body)
        
        mult_url = SEARCH_CONFIG["multi_search_url"]
        body_list_json = json.dumps(body_list)
        
        response = requests.post(mult_url, headers=headers, data=body_list_json, timeout=15)
        
        if response.status_code != 200:
            return json.dumps({"error": f"多搜索请求失败，状态码: {response.status_code}"}, ensure_ascii=False)
        
        response_data = response.json()
        res = [{"author": author.get('author'), "author_info": author.get('author_info'), "articles": data.get('hits')['source']} for author, data in zip(author_source, response_data)]

        return json.dumps(res, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({"error": f"作者搜索失败: {str(e)}"}, ensure_ascii=False)


async def subject_search(subject: str, author: str = None) -> str:
    """
    关键词搜索工具 - 根据主题词搜索学术文献
    
    Args:
        subject: 搜索主题词，多个主题词用分号分隔，如："深度学习;机器学习;人工智能"
        author: 作者姓名，可选参数，用于筛选特定作者的文献
        
    Returns:
        str: JSON格式的搜索结果
    """
    try:
        # 验证参数
        if not subject and not author:
            return json.dumps({"error": "至少需要提供subject或author中的一个参数"}, ensure_ascii=False)
        
        # 构建查询规则
        rule_parts = []
        ruleParam = []
        
        # 处理作者参数
        if author:
            rule_parts.append("(author=x_author)")
            ruleParam.append({"k": "author", "v": author})
        
        # 处理主题词参数
        if subject:
            subject_list = subject.split(";")
            if len(subject_list) == 1:
                subject_list = subject.split(" ")
            subject_rules = []
            for i, sub in enumerate(subject_list):
                subject_rules.append(f"(keyword=phrase_{i})")
                ruleParam.append({"k": str(i), "v": sub})
            
            if subject_rules:
                rule_parts.append("(" + " OR ".join(subject_rules) + ")")
        
        # 组合查询规则
        if len(rule_parts) > 1:
            rule = " AND ".join(rule_parts)
        else:
            rule = rule_parts[0] if rule_parts else ""
         
        url = SEARCH_CONFIG["base_url"]

        payload = json.dumps({
            "tableName": "zt_product_doc",
            "searchMode": 1,
            "owner": "krs",
            "userWord": "",
            "isHighlight": 1,
            "rule": rule,
            "ruleParam": ruleParam,
            "isSynonym": 0,
            "filterRule": "collection=x_1",
            "filterParam": [
                {
                    "k": "1",
                    "v": "krs"
                }
            ],
            "resultField": "title;author;pub_year;abstract",
            "pageNow": 1,
            "pageSize": 50,
            "sort": "",
            "aggsType": "",
            "aggsField": "",
            "filter4count": "",
            "noTruncateResultField": "",
            "fieldTruncateLength": 1000,
            "prioField": "",
            "minShouldMatch": "",
            "refreshCache": False
        }, ensure_ascii=False)
        
        headers = get_search_headers()

        response = requests.post(url, headers=headers, data=payload, timeout=10)
        
        if response.status_code != 200:
            return json.dumps({"error": f"请求失败，状态码: {response.status_code}"}, ensure_ascii=False)
        
        response_data = response.json()
        data = response_data.get('hits')
        
        if not data:
            return json.dumps({"error": f"返回错误内容: {response.text}"}, ensure_ascii=False)
        
        hists = data
        if not hists:
            return json.dumps({"message": "没有搜索到相关内容"}, ensure_ascii=False)
        
        return json.dumps(hists.get('source'), ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({"error": f"主题词搜索失败: {str(e)}"}, ensure_ascii=False)


# 创建工具实例
tka_search_tool = FunctionTool(
    tka_search,
    name="tka_search",
    description="根据篇名、摘要、关键词（篇关摘）同时进行检索，搜索相关文章。支持多搜索词用分号分隔，可选择性添加作者、机构、发表年份等筛选条件"
)

title_search_tool = FunctionTool(
    title_search,
    name="title_search",
    description="根据标题关键词精确搜索学术文献，支持多个标题用分号分隔，专注于标题匹配"
)

author_search_tool = FunctionTool(
    author_search,
    name="author_search",
    description="根据作者姓名、研究主题和机构信息搜索学术文献，支持多维度作者信息检索"
)

subject_search_tool = FunctionTool(
    subject_search,
    name="subject_search",
    description="根据主题关键词搜索学术文献，支持多主题词组合搜索。主题词用分号分隔，支持可选的作者筛选功能"
)
