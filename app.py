import json
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.tools import ToolResult
from autogen_core import CancellationToken
 
from infrastructure import EnhancedWriteMsg, writeLog, Application
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.agents import AssistantAgent, SocietyOfMindAgent
import asyncio
from autogen_agentchat.conditions import TextMentionTermination
from tools import tka_search_tool, title_search_tool, author_search_tool, subject_search_tool
async def choose_data(model_client2 , data_list_all, question, data_list_dict):
    
    literatures_choice_agent = AssistantAgent(
    name="literatures_choice_agent", 
    model_client=model_client2,  # 使用模型客户端
    system_message="""
    你是一个专业的文献筛选助手。你的任务是根据用户提出的问题，从提供的文献列表中识别并选择出与问题最相关的文献。

    **你需要完成以下步骤：**

    1.  **理解用户问题：** 仔细阅读并完全理解用户提出的研究问题或查询。
    2.  **评估文献相关性：** 逐一审查提供的每篇文献的标题和摘要（或您能访问的任何元数据），判断其与用户问题的相关程度。
    3.  **选择最相关文献：** 确定**最多40篇**与问题高度相关，能为解决问题提供关键信息的文献。
    4.  **输出结果：** 以数组格式输出所选文献的`id`。
    
    # 案例：
    - input : {{1:{{...}}}},2:..}}
    - output : [1,2]
    
    **输入：**

    **用户问题：{question}** 

    **文献列表：**
    {literature}
    """.format(question=question, literature=data_list_dict),  # noqa: UP032
    )
    res = await literatures_choice_agent.run()
    data_index_str = res.messages[0].content #JsonOutputParser.parse(res.model_dump_json())
    data_index = json.loads(data_index_str)
    result = []
    for index in data_index:
        result.append(data_list_all[index])
    return result
recorded_literature = []
model_client = OpenAIChatCompletionClient(
    model="qwen-turbo",
    api_key="sk-f08e36beaf794fd59927fdaa1e9e052e",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    # temperature=0.2,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
        "structured_output": True,
    },
)

model_client_plus = OpenAIChatCompletionClient(
    model="qwen-plus",
    api_key="sk-f08e36beaf794fd59927fdaa1e9e052e",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    # temperature=0.2,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
        "structured_output": True,
    },
)
msg_obj = EnhancedWriteMsg()
    
async def runAsync(query: str = ""):
    # 创建增强的消息处理器
    msg_obj.question = query
    
    # 创建工具回调函数来处理工具执行结果
    async def process_tool_result(tool_name: str, result: str):
        """处理工具执行结果"""
        try:
            data_tool = json.loads(result)
            
            # 数据处理阶段
            if "author" in tool_name or "作者搜索" in tool_name:
                msg_obj.data_author = data_tool
            elif "subject" in tool_name or "主题词搜索" in tool_name:
                # subject的数据进行处理
                if data_tool and not isinstance(data_tool, dict) or "error" not in data_tool:
                    data_list_dict = dict()
                    data_list_all = dict()
                    for i, data in enumerate(data_tool):
                        data_list_dict[i+1] = data['title']
                        data_list_all[i+1] = data
                    result_json = await choose_data(model_client, data_list_all=data_list_all, question=msg_obj.question, data_list_dict=data_list_dict)
                    msg_obj.data_keyword = result_json
                    return json.dumps(result_json)
            elif "tka" in tool_name or "篇关摘搜索" in tool_name or "标题搜索" in tool_name:
                # tka的数据进行处理
                if data_tool and not isinstance(data_tool, dict) or "error" not in data_tool:
                    data_list_dict = dict()
                    data_list_all = dict()
                    for i, data in enumerate(data_tool):
                        data_list_dict[i+1] = data['title']
                        data_list_all[i+1] = data
                    result_json = await choose_data(model_client, data_list_all=data_list_all, question=msg_obj.question, data_list_dict=data_list_dict)
                    msg_obj.data_tka = result_json
                    return json.dumps(result_json)
            
            return result
        except Exception as e:
            writeLog(f"处理工具结果时出错: {str(e)}")
            return result
    
    # 修改工具以支持结果处理
    from tools.search_tools import tka_search, title_search, author_search, subject_search
    
    # 创建包装的工具函数
    async def wrapped_tka_search(query: str) -> str:
        result = await tka_search(query)  # 使用query作为tka_all参数
        return await process_tool_result("篇关摘搜索", result)
    
    async def wrapped_title_search(query: str) -> str:
        result = await title_search(query)
        return await process_tool_result("标题搜索", result)
    
    async def wrapped_author_search(search_params: str) -> str:
        result = await author_search(search_params)
        return await process_tool_result("作者搜索", result)
    
    async def wrapped_subject_search(subject: str) -> str:
        result = await subject_search(subject)
        return await process_tool_result("主题词搜索", result)
    
    # 创建新的工具实例
    from autogen_core.tools import FunctionTool
    
    tka_tool = FunctionTool(
        wrapped_tka_search,
        name="tka_search",
        description="根据关键词搜索学术文献的标题、关键词和摘要，支持综合性文献检索"
    )
    
    title_tool = FunctionTool(
        wrapped_title_search,
        name="title_search",
        description="根据标题关键词精确搜索学术文献，专注于标题匹配"
    )
    
    author_tool = FunctionTool(
        wrapped_author_search,
        name="author_search",
        description="根据作者姓名、研究主题和机构信息搜索学术文献，支持多维度作者信息检索"
    )
    
    subject_tool = FunctionTool(
        wrapped_subject_search,
        name="subject_search",
        description="根据主题关键词搜索学术文献，支持多关键词组合搜索"
    )
    # TKA搜索智能体
    comprehensive_search_agent = AssistantAgent(
        name="comprehensive_search_agent",
        description="根据任务进行综合搜索，包括篇关摘、标题搜索等",
        model_client=model_client,
        tools=[tka_tool, title_tool],
        reflect_on_tool_use=False,
        system_message="""你是一个严谨的智能检索助手，任务是根据用户提供的问题调用外部检索工具，搜索出与问题相关的对应文章。
请严格按照以下步骤，并且每一步骤的输出按照输出格式提示，除了有额外提示否则不要跳步骤执行：

    0.  **工具选择** ： 分析任务，根据需要选择一个工具。并说明你选择的工具
    1.  **搜索词生成**：提取关键信息，根据工具描述和字段的描述，生成搜索关键词
        **输出格式：**  
        "**关键词生成**：已根据您的问题"[用户原始问题]"，生成搜索关键词 *(你的关键词)*

    2.  **数据搜索**：使用生成的完整关键词字符串作为参数，选择对应工具进行搜索。
        输出格式："**数据开始进行数据搜索**"
       (在你说完这句话之后，发起对工具的调用) 

    3.  **结果评估与关键词扩展**：
        * 在`篇关摘搜索`工具执行完毕后，你会收到其返回结果。
        * 如果返回结果是找到了相关数据，请直接跳到步骤 4 进行总结。
        * 如果返回 "未找到合适数据"：
            输出格式："**未查询到数据**：初步搜索未能找到与关键词匹配的信息。正在尝试扩展关键词并重新搜索..."
            然后重新生成更广泛的关键词，增加相关术语和同义词
            输出格式："**重新生成关键词**：(新关键词)
            
            然后，再次执行步骤 2 
        * 如果返回 "无关结果":
            输出格式："**文章与问题无关**：查询到的信息与您的问题"[用户原始问题]"关联性不强。正在调整关键词策略..."
            然后生成不同角度的关键词（采用同义词、近义词或相关概念）
            输出格式："**重新生成关键词**：(新关键词)
          
            然后，使用新关键词再次执行步骤 2
        * 你最多允许进行2次重新搜索尝试 (即总共最多3次搜索：1次初始 + 2次重试)。如果2次重试后仍无满意结果，请在步骤4中告知用户未能找到信息。
        
    4.  **回复与总结准备**：
        * 如果找到了相关数据：
            输出格式："**搜索完成**：根据您的提问"[用户原始问题]"已成功找到相关文献数据。"
        * 如果经过最多2次尝试后仍未找到满意数据：
            输出格式："**搜索失败**：根据您的提问"[用户原始问题]"，抱歉，经过多次尝试，未能从搜索引擎中找到相关的详细信息。"
            然后表示需要协调者处理："请协调者评估当前情况并决定下一步行动。"
        
        * **重要提醒**：一旦找到有效数据，立即停止搜索，不要继续重试。
    
    """,
            model_client_stream=True,  # Enable streaming tokens.
        )
        
        # 作者搜索智能体
    author_search_agent = AssistantAgent(
            name="author_search_agent",
            description="根据问题以研究领域和学者为维度进行搜索",
            model_client=model_client,
            tools=[author_tool],
            reflect_on_tool_use=False,
            system_message="""你是一个严谨的智能检索助手，任务是根据用户提供的问题调用外部检索工具，搜索出与问题研究方向相关的作者以及其对应文章。
    请严格按照以下步骤，并且每一步骤的输出按照输出格式提示，除了有额外提示否则不要跳步骤执行：

    1.  **搜索参数生成**：分析用户的问题，提取关键信息，生成搜索参数的JSON格式字典，字典严格遵循以下格式和要求：
        ```json
        {
            "author": "作者名",
            "subject": "学科主题关键词，多个相关词用分号分隔，反映主要研究内容方向，如果问题中没有明确的研究信息则留空",
            "organ": "机构名称，如果问题中没有明确的机构信息则留空"
        }
        ```
        
        生成案例：
        1. 问题："王伟教授所著神经网络的应用场景和使用途径"
           输出：```json
           {
               "author": "王伟",
               "subject": "神经网络;深度学习;人工智能;机器学习;应用场景",
               "organ": ""
           }
           ```
        
        **输出格式：**
        "**搜索参数生成**：已根据您的问题"[用户原始问题]", 搜索作者 *author* ，相关领域为 *subject* ，机构为 *organ*。"

    2.  **数据搜索**：使用生成的搜索参数调用`作者搜索`工具进行搜索。
        输出格式："**数据查询**：正在使用作者搜索引擎查找资料..."
        (将JSON字符串作为参数传递给作者搜索工具)

    3.  **结果评估与参数扩展**：
        * 在`作者搜索`工具执行完毕后，你会收到其返回结果。
        * 如果返回结果是找到了相关数据，请直接跳到步骤 4 进行总结。
        * 如果返回 "未找到合适数据"：
            输出格式："**未查询到数据**：初步搜索未能找到与搜索词匹配的信息。正在尝试扩展搜索参数并重新搜索..."
            然后重新生成更广泛的搜索参数JSON，增加相关术语和同义词
            输出格式："**重新生成搜索参数**：
            ```json
            {新的JSON搜索参数}
            ```"
            然后，使用新搜索参数再次执行步骤 2
        * 如果返回 "无关结果":
            输出格式："**文章与问题无关**：查询到的信息与您的问题"[用户原始问题]"关联性不强。正在调整搜索策略..."
            然后生成不同角度的搜索参数JSON（采用同义词、近义词或相关概念）
            输出格式："**重新生成搜索参数**：
            ```json
            {调整后的JSON搜索参数}
            ```"
            然后，使用新搜索参数再次执行步骤 2
        * 你最多允许进行2次重新搜索尝试 (即总共最多3次搜索：1次初始 + 2次重试)。如果2次重试后仍无满意结果，请在步骤4中告知用户未能找到信息。
        
    4.  **回复与总结准备**：
        * 如果找到了相关数据：
            输出格式："**搜索完成**：根据您的提问"[用户原始问题]"已成功找到相关文献数据。"
        * 如果经过最多2次尝试后仍未找到满意数据：
            输出格式："**搜索失败**：根据您的提问"[用户原始问题]"，抱歉，经过多次尝试，未能从搜索引擎中找到相关的详细信息。"
            然后表示需要协调者处理："请协调者评估当前情况并决定下一步行动。"
        
        * **重要提醒**：一旦找到有效数据，立即停止搜索，不要继续重试。
    
    """,
            model_client_stream=True,  # Enable streaming tokens.
        )

        # 主题词词搜索智能体
    subject_search_agent = AssistantAgent(
            name="subject_search_agent",
            description="根据问题进行关键词搜索",
            model_client=model_client,
            tools=[subject_tool],
            reflect_on_tool_use=True,
            system_message="""你是一个严谨的智能检索助手，任务是根据用户提供的问题调用外部检索工具，搜索出相关文章。
    请严格按照以下步骤，并且每一步骤的输出按照输出格式提示，除了有额外提示否则不要跳步骤执行：

    1.  **搜索参数生成**：分析任务，提取关键信息，生成搜索参数的JSON格式字典，字典严格遵循以下格式和要求：
        ```json
        {
            "subject": "关键词，多个词用分号分隔，反映核心研究概念",
            "author": "作者
        }
        ```
        
        生成案例：
        1. 问题："王伟教授所著神经网络的应用场景和使用途径"
           输出：```json
           {
               "subject": "神经网络;深度学习;人工智能;机器学习;应用场景",
               "author": "王伟"
           }
           ```
                 
        **输出格式：**
        "**搜索参数生成**：已根据您的问题"[用户原始问题]"，生成搜索主题参数 *subject* "

    2.  **数据搜索**：使用生成的搜索参数调用`主题词搜索`工具进行搜索。
        输出格式："**数据查询**：正在使用主题词搜索引擎查找资料..."
        (将JSON中的subject和author组合成空格分隔的字符串，作为参数传递给主题词搜索工具)

    3.  **结果评估与参数扩展**：
        * 在`主题词搜索`工具执行完毕后，你会收到其返回结果。
        * 如果返回结果是找到了相关数据，请直接跳到步骤 4 进行总结。
        * 如果返回 "未找到合适数据"：
            输出格式："**未查询到数据**：初步搜索未能找到与关键词匹配的信息。正在尝试扩展搜索参数并重新搜索..."
            然后重新生成更广泛的搜索参数JSON，增加相关术语和同义词
            输出格式："**重新生成搜索参数**：
            ```json
            {新的JSON搜索参数}
            ```"
            然后，使用新搜索参数再次执行步骤 2
        * 如果返回 "无关结果":
            输出格式："**文章与问题无关**：查询到的信息与您的问题"[用户原始问题]"关联性不强。正在调整搜索策略..."
            然后生成不同角度的搜索参数JSON（采用同义词、近义词或相关概念）
            输出格式："**重新生成搜索参数**：
            ```json
            {调整后的JSON搜索参数}
            ```"
            然后，使用新搜索参数再次执行步骤 2
        * 你最多允许进行1次重新搜索尝试 (即总共最多2次搜索：1次初始 + 1次重试)。如果1次重试后仍无满意结果，请在步骤4中告知用户未能找到信息。

    4.  **回复与总结准备**：
        * 如果找到了相关数据：
            输出格式："**搜索完成**：根据您的提问"[用户原始问题]"已成功找到相关文献数据。"
        * 如果经过最多2次尝试后仍未找到满意数据：
            输出格式："**搜索失败**：根据您的提问"[用户原始问题]"，抱歉，经过多次尝试，未能从搜索引擎中找到相关的详细信息。"
            然后表示需要协调者处理："请协调者评估当前情况并决定下一步行动。"
        
        * **重要提醒**：一旦找到有效数据，立即停止搜索，不要继续重试。

    """,
            model_client_stream=True,  # Enable streaming tokens.
        )

        # 创建协调者Agent
    coordinator_agent = AssistantAgent(
            "CoordinatorAgent",
            model_client=model_client_plus,
            description="负责任务分析、计划制定和全流程协调的智能协调者",
            model_client_stream=True,  # Enable streaming tokens.
            system_message=f"""
你是文献搜索系统的高级智能协调者，具备强大的任务规划、智能体调度和质量管控能力。你的核心使命是根据用户问题的特征和复杂度，制定最优的搜索策略，并动态调度各专业智能体协同工作。

=== 核心职责 ===

**1. 任务规划与预期管理**
- 深度解析用户问题的意图、范围和复杂度
- 制定详细的搜索任务计划和预期目标
- 建立各阶段的里程碑和验收标准
- 管理整体项目进度和资源分配

**2. 智能体自由调度**
- 动态分配任务，确保从多个入口获取文献资源
- 为每个智能体提供个性化的任务要求和执行指导
- 实时调整调度策略以适应搜索进展

**3. 质量验收与策略优化**
- 严格验收每个智能体的输出质量
- 识别不满足预期的结果并提供改进建议
- 指导智能体采用更合适的搜索策略

===  智能体能力分析 ===

**comprehensive_search_agent（综合搜索专家）**
- 核心专长：综合搜索，多维度匹配
- 最佳场景：需要全面了解研究主题、从多维度获取文献
- 调度指令：为其提供完整的研究主题和深度要求

**author_search_agent（作者搜索专家）**
- 核心专长：作者和机构精准定位
- 最佳场景：明确的作者查询、机构研究分析
- 调度指令：提供具体的作者名称、机构信息和期望产出

**subject_search_agent（关键词搜索专家）**
- 核心专长：专业术语和概念精确检索
- 最佳场景：特定技术领域、概念探索
- 调度指令：提供核心关键词和搜索深度要求

=== 智能调度决策流程 ===

**阶段1：问题智能分析**
输出格式："**问题分析**：[问题类型]  | [关键要素识别]"

判断问题类型：
- **精准定向型**：明确指向特定作者/机构/技术 → 选择性调度
- **综合探索型**：需要全面了解某领域/主题 → 全面调度  
- **混合研究型**：既有明确目标又需要拓展 → 分层调度

**阶段2：调度策略制定**
输出格式："** 调度策略**：[选择的智能体] | [调度理由] | [预期目标]"

调度策略选择：
- **单点突破**：问题指向明确，选择1-2个最匹配的智能体
- **多点并行**：需要全面信息，同时调度2-3个智能体
- **分层递进**：先精准搜索，再根据结果决定是否扩展

**阶段3：任务分配与执行**
为每个选定的智能体提供：
- **具体任务**：明确的搜索目标和范围
- **预期产出**：期望获得的文献数量和质量
- **执行指导**：搜索策略和注意事项

**阶段4：过程监控与质量验收**
对每个智能体的输出进行评估：
- **内容相关性**：是否匹配用户问题的核心需求
- **数据完整性**：文献信息是否完整、准确
- **目标达成度**：是否满足预期的搜索目标

**验收不通过时的处理：**
-  **策略调整**：提供具体的改进建议和新的搜索方向
-  **重新分配**：必要时调用其他智能体补充或替代

**阶段5：总结触发与结果整合**
所有任务完成并通过验收后：
输出格式："** 搜索完成，启动总结**：已完成[具体描述的任务]，获得[X]条高质量文献，现在请SummaryAgent进行综合分析。"

=== 调度示例 ===

**示例1：精准定向型** - "清华大学李明教授的人工智能研究"
- 调度决策：仅调用author_search_agent
- 任务指令："精准搜索李明教授在清华大学的人工智能相关研究成果"
- 预期目标：获得该作者的完整研究作品列表

**示例2：综合探索型** - "深度学习在医学影像中的应用发展"
- 调度决策：全面调度三个智能体
- comprehensive_search_agent："从多维度搜索深度学习医学影像的综合文献"
- subject_search_agent："专门搜索深度学习、医学影像、图像处理等核心技术术语"
- author_search_agent："识别该领域的重要研究者和机构"

**示例3：混合研究型** - "重庆大学在区块链技术方面的研究进展"
- 调度决策：分层递进调度
- 第一层：author_search_agent搜索重庆大学的区块链研究
- 第二层：根据结果决定是否用comprehensive_search_agent扩展搜索

=== 执行原则 ===

1. **效率优先**：根据问题特征选择最高效的调度方案
2. **质量保障**：严格验收，确保每个环节的输出质量
3. **动态调整**：根据实际搜索效果灵活调整策略
4. **用户导向**：始终以满足用户的核心需求为最高目标
5. **资源优化**：避免不必要的重复搜索，提高整体效率

你的目标是成为最智能的任务协调者，通过精准的问题分析、灵活的智能体调度和严格的质量管控，为用户提供最优质的文献搜索服务。
"""
        
        )
        
    # 创建总结agent
    summary_agent = AssistantAgent(
            "SummaryAgent",
            model_client=model_client,
            tools=[],
            description="负责汇总所有搜索结果并生成总结报告",
            model_client_stream=True,  # Enable streaming tokens.
            system_message="""
        你是文献综述专家，擅长将多源搜索结果整合为结构化的总结报告。
        你需要遵循协调者的指导，执行以下任务：
        
        1. 总结触发：
           - 仅当协调者使用"建议总结"、"搜索完成"或"启动总结流程"等关键词后，立即启动总结流程
           - 确保已有足够的文献数据可供总结
        
        2. 总结执行：
           - 分析对话历史中的搜索结果和文献信息
           - 整合所有搜索智能体提供的文献数据
        
        3. 报告生成：
           - 创建结构化的总结报告，包括：
             - 文献总体概述（数量、主题分布）
             - 主要研究方向和成果
             - 重要文献推荐（高相关性文献）
             - 研究趋势和未来方向
           - 针对用户原始问题进行针对性回答
        
        4. 协作响应：
           - 当协调者要求补充或调整总结内容时，及时响应
           - 提供清晰、有条理的总结结果，便于理解和使用
        
        5. 流程结束：
           - 当总结报告完成后，在回复的最后明确使用"总结完成"关键词
           - 这表示整个搜索流程已经完成，系统将自动结束对话
        
        === 总结格式要求 ===
        - 开头：明确回应协调者的总结请求
        - 主体：结构化的文献分析报告
        - 结尾：使用"总结完成"关键词结束流程
        
        === 立即响应 ===
        当收到协调者的总结请求时，立即开始总结，不要等待其他指示。
        """
        )

    selector_prompt = """你需要根据当前对话状态和CoordinatorAgent的任务分配指示，选择合适的智能体来执行下一步任务。

    智能体职能说明：
    {roles}
    
    当前对话上下文：
    {history}
    
    选择原则：
    1. **优先执行CoordinatorAgent**：如果对话刚开始或需要新的任务分析，选择CoordinatorAgent进行问题分析和任务分配
    2. **按分配执行搜索智能体**：当CoordinatorAgent已经分配了具体任务时，按照其指示选择对应的搜索智能体
    3. **动态调整**：如果搜索结果不满意需要重新分配，优先选择CoordinatorAgent进行策略调整
    
    请从 {participants} 中选择最合适的智能体执行下一个任务，并简要说明选择理由。
    """
    termination = TextMentionTermination("总结完成")
    # 初始化SelectorGroupChat团队
    team = SelectorGroupChat(
        participants=[author_search_agent, subject_search_agent, comprehensive_search_agent, summary_agent, coordinator_agent],
        model_client=model_client_plus,
        selector_prompt=selector_prompt,
        termination_condition=termination,
        model_client_streaming=True,
        allow_repeated_speaker=True,
        max_turns=10  # 防止无限循环
    )
        
    society_of_mind_agent = SocietyOfMindAgent("society_of_mind", model_client=model_client, team=team)
    data_all = ""
    
    # 使用传入的查询参数，如果没有则使用默认值
    task_query = query if query else "特种设备有什么制作途径"
    data_list = []
    async for message in society_of_mind_agent.on_messages_stream(
        [TextMessage(content=query, source="user_proxy")],
        cancellation_token=CancellationToken()):
        # try:
        #     writeLog(message.to_text())
        # except:
        #     pass
        # 处理标准消息
        data = msg_obj.writeSSEMessage(message)
        if data:
            yield data
            data_all += data['text']
            if data.get('data'):
                data_list = data['data']
    
    # 返回工具执行摘要
    # tool_summary = msg_obj.get_tool_execution_summary()
    # yield "data:" + json.dumps({
    #     "type": "tool_summary",
    #     "data": data_list,
    #     "tool_executions": tool_summary
    # }, ensure_ascii=False) + "\n\n"

    writeLog(data_all)

if __name__ == "__main__":
   methodPtr=lambda x: runAsync(x.query_params.get("query") )
   Application(methodPtr).run("agent_krs")
   
     
