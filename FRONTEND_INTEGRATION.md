# 前端集成文档 - 消息类型说明

## 概述

系统通过 Server-Sent Events (SSE) 返回三种不同类型的消息，前端可以根据 `type` 字段来区分并展示不同的内容和步骤。

## 消息类型详解

### 1. agent_thinking - 检索agent的思考过程

**用途**: 显示AI智能体的思考过程和推理步骤

**消息结构**:
```json
{
  "type": "agent_thinking",
  "step": 1,
  "step_name": "检索agent的思考过程",
  "text": "正在分析用户问题...",
  "source": "CoordinatorAgent"
}
```

**字段说明**:
- `type`: 固定值 "agent_thinking"
- `step`: 步骤编号，固定为 1
- `step_name`: 步骤名称
- `text`: 智能体的思考内容
- `source`: 产生消息的智能体名称

**前端展示建议**:
- 实时流式展示思考过程
- 可以按不同智能体分组显示
- 使用打字机效果展示文本流

### 2. search_result - 检索结果结构化返回

**用途**: 返回结构化的搜索结果数据

**消息结构**:
```json
{
  "type": "search_result",
  "step": 2,
  "step_name": "检索结果结构化返回",
  "text": "TKA搜索完成，找到15条结果",
  "data": [
    {
      "title": "论文标题",
      "author": "作者姓名",
      "abstract": "摘要内容"
    }
  ],
  "source": "comprehensive_search_agent",
  "search_type": "TKA搜索"
}
```

**字段说明**:
- `type`: 固定值 "search_result"
- `step`: 步骤编号，固定为 2
- `step_name`: 步骤名称
- `text`: 搜索结果描述
- `data`: 结构化的搜索结果数组
- `source`: 执行搜索的智能体
- `search_type`: 搜索类型（TKA搜索、主题词搜索、作者搜索等）

**前端展示建议**:
- 以表格或卡片形式展示搜索结果
- 按搜索类型分组显示
- 提供筛选和排序功能
- 显示搜索进度和结果统计

### 3. final_summary - 最后的总结

**用途**: 返回完整的总结报告和最终数据

**消息结构**:
```json
{
  "type": "final_summary",
  "step": 3,
  "step_name": "最后的总结",
  "query": "用户原始查询",
  "statistics": {
    "total_literature": 45,
    "total_authors": 12,
    "total_subjects": 8
  },
  "literature_list": [
    // 去重后的文献列表（最多50篇）
  ],
  "author_list": [
    // 相关作者列表（最多20位）
  ],
  "subject_list": [
    // 主题词列表
  ],
  "summary_text": "完整的总结文本",
  "source": "system"
}
```

**字段说明**:
- `type`: 固定值 "final_summary"
- `step`: 步骤编号，固定为 3
- `step_name`: 步骤名称
- `query`: 用户的原始查询
- `statistics`: 数据统计信息
- `literature_list`: 去重后的有效文献列表
- `author_list`: 相关作者信息
- `subject_list`: 提取的主题词
- `summary_text`: 完整的总结报告文本
- `source`: 消息来源

**前端展示建议**:
- 显示完整的总结报告
- 提供数据统计面板
- 支持文献列表的导出功能
- 展示作者和主题词的关联图表

## 前端实现示例

### JavaScript/TypeScript 示例

```javascript
// 监听SSE流
const eventSource = new EventSource('/sse?query=' + encodeURIComponent(query));

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'agent_thinking':
      // 步骤1: 显示智能体思考过程
      updateThinkingProcess(data);
      break;
      
    case 'search_result':
      // 步骤2: 显示搜索结果
      updateSearchResults(data);
      break;
      
    case 'final_summary':
      // 步骤3: 显示最终总结
      updateFinalSummary(data);
      break;
  }
  
  // 更新进度条
  updateProgress(data.step, data.step_name);
};

function updateThinkingProcess(data) {
  // 实时更新思考过程
  const thinkingContainer = document.getElementById('thinking-process');
  const agentSection = getOrCreateAgentSection(data.source);
  agentSection.innerHTML += data.text;
}

function updateSearchResults(data) {
  // 更新搜索结果
  const resultsContainer = document.getElementById('search-results');
  if (data.data && data.data.length > 0) {
    const searchSection = createSearchResultSection(data.search_type, data.data);
    resultsContainer.appendChild(searchSection);
  }
}

function updateFinalSummary(data) {
  // 显示最终总结
  document.getElementById('statistics').innerHTML = createStatisticsView(data.statistics);
  document.getElementById('literature-list').innerHTML = createLiteratureList(data.literature_list);
  document.getElementById('author-list').innerHTML = createAuthorList(data.author_list);
  document.getElementById('summary-text').innerHTML = data.summary_text;
}
```

### Vue.js 示例

```vue
<template>
  <div class="search-interface">
    <!-- 进度指示器 -->
    <div class="progress-indicator">
      <div class="step" :class="{active: currentStep >= 1}">
        1. 检索agent的思考过程
      </div>
      <div class="step" :class="{active: currentStep >= 2}">
        2. 检索结果结构化返回
      </div>
      <div class="step" :class="{active: currentStep >= 3}">
        3. 最后的总结
      </div>
    </div>
    
    <!-- 思考过程展示 -->
    <div v-if="currentStep >= 1" class="thinking-section">
      <h3>智能体思考过程</h3>
      <div v-for="thinking in thinkingProcess" :key="thinking.id">
        <div class="agent-thinking">
          <span class="agent-name">{{ thinking.source }}</span>
          <span class="thinking-text">{{ thinking.text }}</span>
        </div>
      </div>
    </div>
    
    <!-- 搜索结果展示 -->
    <div v-if="currentStep >= 2" class="results-section">
      <h3>搜索结果</h3>
      <div v-for="result in searchResults" :key="result.id">
        <h4>{{ result.search_type }}</h4>
        <div class="result-items">
          <div v-for="item in result.data" :key="item.id" class="result-item">
            <h5>{{ item.title }}</h5>
            <p>作者: {{ item.author }}</p>
            <p>{{ item.abstract }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 最终总结 -->
    <div v-if="currentStep >= 3" class="summary-section">
      <h3>最终总结</h3>
      <div class="statistics">
        <div>文献数量: {{ finalSummary?.statistics.total_literature }}</div>
        <div>作者数量: {{ finalSummary?.statistics.total_authors }}</div>
        <div>主题词数量: {{ finalSummary?.statistics.total_subjects }}</div>
      </div>
      <div class="summary-text">{{ finalSummary?.summary_text }}</div>
      
      <!-- 文献列表 -->
      <div class="literature-section">
        <h4>相关文献</h4>
        <div v-for="lit in finalSummary?.literature_list" :key="lit.title" class="literature-item">
          <h5>{{ lit.title }}</h5>
          <p>{{ lit.author }}</p>
        </div>
      </div>
      
      <!-- 作者列表 -->
      <div class="author-section">
        <h4>相关作者</h4>
        <div v-for="author in finalSummary?.author_list" :key="author.name" class="author-item">
          <span>{{ author.name }}</span>
          <span>({{ author.articles_count }}篇文章)</span>
        </div>
      </div>
      
      <!-- 主题词列表 -->
      <div class="subject-section">
        <h4>主题词</h4>
        <div class="subject-tags">
          <span v-for="subject in finalSummary?.subject_list" :key="subject" class="subject-tag">
            {{ subject }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 0,
      thinkingProcess: [],
      searchResults: [],
      finalSummary: null
    };
  },
  methods: {
    startSearch(query) {
      const eventSource = new EventSource(`/sse?query=${encodeURIComponent(query)}`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      };
    },
    
    handleMessage(data) {
      this.currentStep = Math.max(this.currentStep, data.step);
      
      switch(data.type) {
        case 'agent_thinking':
          this.thinkingProcess.push({
            id: Date.now() + Math.random(),
            ...data
          });
          break;
        case 'search_result':
          if (data.data && data.data.length > 0) {
            this.searchResults.push({
              id: Date.now() + Math.random(),
              ...data
            });
          }
          break;
        case 'final_summary':
          this.finalSummary = data;
          break;
      }
    }
  }
};
</script>

<style scoped>
.progress-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.step {
  padding: 10px;
  background: #f0f0f0;
  border-radius: 5px;
  flex: 1;
  margin: 0 5px;
  text-align: center;
}

.step.active {
  background: #007bff;
  color: white;
}

.thinking-section, .results-section, .summary-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.agent-thinking {
  margin: 10px 0;
  padding: 8px;
  background: #f8f9fa;
  border-left: 3px solid #007bff;
}

.agent-name {
  font-weight: bold;
  color: #007bff;
  margin-right: 10px;
}

.result-item, .literature-item {
  margin: 10px 0;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 5px;
}

.statistics {
  display: flex;
  gap: 20px;
  margin: 15px 0;
}

.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.subject-tag {
  background: #e9ecef;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9em;
}

.author-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}
</style>
```

## 注意事项

1. **消息顺序**: 消息按步骤顺序发送，但同一步骤内可能有多条消息
2. **错误处理**: 建议添加错误处理机制，监听 SSE 错误事件
3. **性能优化**: 对于大量数据，建议使用虚拟滚动等优化技术
4. **用户体验**: 提供加载状态指示器和进度反馈
5. **数据持久化**: 考虑将最终结果保存到本地存储

## 测试数据示例

可以使用以下查询进行测试：
- "人工智能在医疗领域的应用"
- "张三教授的机器学习研究"
- "深度学习算法优化"

这些查询会触发不同类型的搜索，帮助测试前端的消息处理逻辑。
