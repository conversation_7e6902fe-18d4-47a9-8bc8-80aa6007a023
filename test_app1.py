#!/usr/bin/env python3
"""
测试修改后的app1.py文件
验证消息类型和数据结构是否符合前端需求
"""

import asyncio
import json
from app1 import runAsync

async def test_message_types():
    """测试不同消息类型的输出"""
    print("开始测试检索智能体服务...")
    
    # 测试查询
    test_query = "深度学习在医学影像中的应用"
    
    print(f"测试查询: {test_query}")
    print("=" * 50)
    
    message_count = 0
    message_types = {}
    
    try:
        async for message_data in runAsync(test_query):
            if message_data.startswith("data:"):
                message_count += 1
                try:
                    # 解析JSON数据
                    json_str = message_data[5:].strip()  # 移除"data:"前缀
                    if json_str:
                        data = json.loads(json_str)
                        msg_type = data.get('type', 'unknown')
                        
                        # 统计消息类型
                        if msg_type not in message_types:
                            message_types[msg_type] = 0
                        message_types[msg_type] += 1
                        
                        # 打印消息摘要
                        print(f"消息 #{message_count}")
                        print(f"  类型: {msg_type}")
                        print(f"  步骤: {data.get('step', 'N/A')} - {data.get('step_name', 'N/A')}")
                        print(f"  文本: {data.get('text', '')[:100]}...")
                        
                        # 如果有数据，显示数据统计
                        if 'data' in data and data['data']:
                            if isinstance(data['data'], list):
                                print(f"  数据项数: {len(data['data'])}")
                            elif isinstance(data['data'], dict):
                                if 'items' in data['data']:
                                    print(f"  数据项数: {len(data['data']['items'])}")
                                print(f"  数据类型: {data['data'].get('data_type', 'unknown')}")
                        
                        print(f"  来源: {data.get('source', 'unknown')}")
                        print("-" * 30)
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"原始数据: {message_data[:200]}...")
                    
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"总消息数: {message_count}")
    print("消息类型统计:")
    for msg_type, count in message_types.items():
        print(f"  {msg_type}: {count} 条")
    
    # 验证是否包含所需的消息类型
    required_types = ['agent_thinking', 'search_result', 'structured_data', 'final_summary']
    missing_types = [t for t in required_types if t not in message_types]
    
    if missing_types:
        print(f"\n⚠️  缺少的消息类型: {missing_types}")
    else:
        print("\n✅ 所有必需的消息类型都已包含")

def test_message_structure():
    """测试消息结构是否符合要求"""
    print("\n" + "=" * 50)
    print("消息结构要求验证:")
    print("1. agent_thinking - 展示agent的思考过程")
    print("2. search_result - 展示搜索过程和结果")
    print("3. structured_data - 结构化数据(文献、作者、主题词)")
    print("4. final_summary - 最终总结结果")
    print("\n每个消息应包含:")
    print("- type: 消息类型")
    print("- text: 消息文本")
    print("- source: 消息来源")
    print("- timestamp: 时间戳")
    print("- step: 步骤编号")
    print("- step_name: 步骤名称")
    print("- data: 结构化数据(可选)")
    print("- metadata: 元数据(可选)")

if __name__ == "__main__":
    print("检索智能体服务测试")
    print("=" * 50)
    
    # 显示测试说明
    test_message_structure()
    
    # 运行异步测试
    asyncio.run(test_message_types())
